@echo off
echo ========================================
echo     GivingItem 插件部署脚本
echo ========================================
echo.

:: 检查是否存在构建好的JAR文件
if not exist "target\GivingItem-1.0.jar" (
    echo 错误：未找到构建好的JAR文件！
    echo 请先运行 mvn clean package 命令构建插件。
    pause
    exit /b 1
)

:: 显示JAR文件信息
echo 插件文件: target\GivingItem-1.0.jar
for %%A in (target\GivingItem-1.0.jar) do echo 文件大小: %%~zA 字节
echo.

:: 询问用户是否要复制到服务器
set /p copy_to_server="是否要将插件复制到Minecraft服务器？(y/n): "
if /i "%copy_to_server%"=="y" (
    set /p server_path="请输入服务器plugins目录路径: "
    if exist "!server_path!" (
        copy "target\GivingItem-1.0.jar" "!server_path!\GivingItem-1.0.jar"
        echo 插件已复制到服务器目录！
    ) else (
        echo 错误：指定的服务器目录不存在！
    )
)

echo.
echo ========================================
echo           部署完成！
echo ========================================
echo.
echo 使用说明：
echo 1. 将 GivingItem-1.0.jar 放入服务器的 plugins 目录
echo 2. 重启服务器或使用 /reload 命令
echo 3. 编辑 plugins/GivingItem/config.yml 配置文件
echo 4. 使用 /givingitem reload 重新加载配置
echo.
echo 主要命令：
echo - /givingitem help     显示帮助信息
echo - /givingitem reload   重新加载配置
echo - /givingitem give ^<玩家^>  给指定玩家发放物品
echo - /givingitem info     显示插件信息
echo.
pause
