# GivingItem 插件

一个为 Spigot 1.8.8 开发的 Minecraft 插件，用于在玩家首次加入服务器时自动给予可配置的物品。

## 功能特性

- ✅ 玩家首次加入时自动给予物品
- ✅ 物品右键点击执行自定义命令
- ✅ 可配置物品类型、名称、槽位
- ✅ 防止物品丢弃和移动
- ✅ 支持多种命令执行模式（玩家/控制台/OP）
- ✅ 支持占位符（%player%, %uuid%, %world%）
- ✅ 完整的配置文件系统
- ✅ 管理员命令支持

## 安装方法

1. 将编译好的 `GivingItem-1.0.jar` 文件放入服务器的 `plugins` 目录
2. 重启服务器或使用 `/reload` 命令
3. 编辑 `plugins/GivingItem/config.yml` 配置文件
4. 使用 `/givingitem reload` 重新加载配置

## 配置文件

### config.yml 示例

```yaml
# GivingItem 插件配置文件
# 配置玩家首次加入时获得的物品

# 物品配置列表
items:
  # 传送菜单指南针
  - material: COMPASS
    name: "&a传送菜单"
    slot: 4
    command: "warp gui"
    executor: PLAYER
  
  # 帮助手册
  - material: BOOK
    name: "&b帮助手册"
    slot: 8
    command: "help"
    executor: PLAYER

# 插件设置
settings:
  # 是否在玩家首次加入时给予物品
  give-items-on-first-join: true

  # 是否在给予物品前清空玩家背包
  # 注意：这只会清空主背包（0-35槽位），不会影响盔甲槽和副手槽
  # 建议在新服务器或重置服务器时启用此功能
  clear-inventory-before-giving: false

  # 是否阻止玩家丢弃这些物品
  prevent-drop: true

  # 是否阻止玩家移动这些物品
  prevent-move: true

  # 调试模式
  debug: false
```

### 配置说明

#### 物品配置 (items)
- `material`: 物品材质（如 COMPASS, BOOK, DIAMOND_SWORD 等）
- `name`: 物品显示名称（支持颜色代码 &a, &b 等）
- `slot`: 物品在背包中的槽位（0-35）
- `command`: 右键点击时执行的命令
- `executor`: 命令执行模式
  - `PLAYER`: 以玩家身份执行
  - `CONSOLE`: 以控制台身份执行
  - `OP`: 以OP权限执行

#### 插件设置 (settings)
- `give-items-on-first-join`: 是否在首次加入时给予物品
- `clear-inventory-before-giving`: 是否在给予物品前清空玩家背包（仅清空主背包0-35槽位）
- `prevent-drop`: 是否阻止丢弃物品
- `prevent-move`: 是否阻止移动物品
- `debug`: 调试模式，输出详细日志

## 命令

| 命令 | 描述 | 权限 |
|------|------|------|
| `/givingitem` | 显示帮助信息 | 无 |
| `/givingitem reload` | 重新加载配置文件 | `givingitem.reload` |
| `/givingitem give <玩家名>` | 给指定玩家发放物品 | `givingitem.give` |
| `/givingitem info` | 显示插件信息 | 无 |

### 命令别名
- `/gi`
- `/gitem`

## 权限

| 权限 | 描述 | 默认 |
|------|------|------|
| `givingitem.reload` | 重新加载配置文件 | OP |
| `givingitem.give` | 给玩家发放物品 | OP |
| `givingitem.*` | 所有权限 | OP |

## 占位符

在命令中可以使用以下占位符：

- `%player%`: 玩家名称
- `%uuid%`: 玩家UUID
- `%world%`: 玩家所在世界名称

## 编译方法

1. 确保已安装 Java 8 或更高版本
2. 运行 `build.bat` 脚本
3. 编译完成后在 `build` 目录中找到 JAR 文件

## 技术规范

- **目标版本**: Spigot 1.8.8
- **Java版本**: Java 8+
- **编码规范**: 阿里巴巴Java编码规范
- **API版本**: 1.8

## 更新日志

### v1.0
- 初始版本发布
- 实现基础功能
- 支持配置文件
- 添加管理员命令

## 许可证

本项目采用 MIT 许可证。

## 支持

如有问题或建议，请提交 Issue 或联系开发者。
