package GivingItem;

import org.bukkit.Material;

/**
 * 给予物品的数据类
 * 封装了物品的所有配置信息
 *
 * <AUTHOR>
 * @version 1.0
 */
public class GivingItemData {
    
    /** 物品材质 */
    private final Material material;
    
    /** 物品显示名称 */
    private final String name;
    
    /** 物品在背包中的槽位 */
    private final int slot;
    
    /** 右键执行的命令 */
    private final String command;
    
    /** 命令执行器类型 */
    private final CommandExecutorType executorType;
    
    /**
     * 构造函数
     * 
     * @param material 物品材质
     * @param name 物品显示名称
     * @param slot 物品槽位
     * @param command 执行命令
     * @param executorType 执行器类型
     */
    public GivingItemData(Material material, String name, int slot, String command, CommandExecutorType executorType) {
        this.material = material;
        this.name = name;
        this.slot = slot;
        this.command = command;
        this.executorType = executorType;
    }
    
    /**
     * 获取物品材质
     * 
     * @return 物品材质
     */
    public Material getMaterial() {
        return material;
    }
    
    /**
     * 获取物品显示名称
     * 
     * @return 物品显示名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取物品槽位
     * 
     * @return 物品槽位
     */
    public int getSlot() {
        return slot;
    }
    
    /**
     * 获取执行命令
     * 
     * @return 执行命令
     */
    public String getCommand() {
        return command;
    }
    
    /**
     * 获取命令执行器类型
     * 
     * @return 命令执行器类型
     */
    public CommandExecutorType getExecutorType() {
        return executorType;
    }
    
    /**
     * 验证物品数据是否有效
     * 
     * @return 如果数据有效返回true，否则返回false
     */
    public boolean isValid() {
        return material != null 
            && name != null && !name.trim().isEmpty()
            && slot >= 0 && slot <= 35
            && command != null && !command.trim().isEmpty()
            && executorType != null;
    }
    
    @Override
    public String toString() {
        return "GivingItemData{" +
                "material=" + material +
                ", name='" + name + '\'' +
                ", slot=" + slot +
                ", command='" + command + '\'' +
                ", executorType=" + executorType +
                '}';
    }
}
