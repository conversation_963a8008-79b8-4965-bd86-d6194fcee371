package GivingItem;

import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;

/**
 * GivingItem 插件主类
 * 负责插件的启动、关闭和命令处理
 *
 * <AUTHOR>
 * @version 1.0
 */
public class GivingItem extends JavaPlugin {

    private ConfigManager configManager;
    private PlayerListener playerListener;

    /**
     * 插件启用时调用
     */
    @Override
    public void onEnable() {
        getLogger().info("GivingItem 插件正在启用...");

        // 初始化配置管理器
        configManager = new ConfigManager(this);

        // 初始化事件监听器
        playerListener = new PlayerListener(this, configManager);

        // 注册事件监听器
        Bukkit.getPluginManager().registerEvents(playerListener, this);

        getLogger().info("GivingItem 插件启用完成！");
    }

    /**
     * 插件禁用时调用
     */
    @Override
    public void onDisable() {
        getLogger().info("GivingItem 插件已禁用！");
    }

    /**
     * 处理命令
     *
     * @param sender 命令发送者
     * @param command 命令对象
     * @param label 命令标签
     * @param args 命令参数
     * @return 命令是否处理成功
     */
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (command.getName().equalsIgnoreCase("givingitem")) {
            return handleGivingItemCommand(sender, args);
        }

        return false;
    }

    /**
     * 处理 givingitem 命令
     *
     * @param sender 命令发送者
     * @param args 命令参数
     * @return 命令是否处理成功
     */
    private boolean handleGivingItemCommand(CommandSender sender, String[] args) {
        if (args.length == 0) {
            sender.sendMessage("§a=== GivingItem 插件帮助 ===");
            sender.sendMessage("§e/givingitem reload §7- 重新加载配置文件");
            sender.sendMessage("§e/givingitem give <玩家名> §7- 给指定玩家发放物品");
            sender.sendMessage("§e/givingitem info §7- 显示插件信息");
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "reload":
                return handleReloadCommand(sender);

            case "give":
                return handleGiveCommand(sender, args);

            case "info":
                return handleInfoCommand(sender);

            default:
                sender.sendMessage("§c未知的子命令: " + subCommand);
                return false;
        }
    }

    /**
     * 处理重新加载命令
     *
     * @param sender 命令发送者
     * @return 命令是否处理成功
     */
    private boolean handleReloadCommand(CommandSender sender) {
        if (!sender.hasPermission("givingitem.reload")) {
            sender.sendMessage("§c你没有权限执行此命令！");
            return true;
        }

        try {
            configManager.loadConfig();
            sender.sendMessage("§a配置文件重新加载成功！");
            getLogger().info("配置文件已被 " + sender.getName() + " 重新加载");
        } catch (Exception e) {
            sender.sendMessage("§c配置文件重新加载失败: " + e.getMessage());
            getLogger().warning("配置文件重新加载失败: " + e.getMessage());
        }

        return true;
    }

    /**
     * 处理给予物品命令
     *
     * @param sender 命令发送者
     * @param args 命令参数
     * @return 命令是否处理成功
     */
    private boolean handleGiveCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("givingitem.give")) {
            sender.sendMessage("§c你没有权限执行此命令！");
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage("§c用法: /givingitem give <玩家名>");
            return true;
        }

        String playerName = args[1];
        Player targetPlayer = Bukkit.getPlayer(playerName);

        if (targetPlayer == null) {
            sender.sendMessage("§c玩家 " + playerName + " 不在线或不存在！");
            return true;
        }

        // 给玩家发放物品
        giveItemsToPlayer(targetPlayer);
        sender.sendMessage("§a已给玩家 " + targetPlayer.getName() + " 发放物品！");

        return true;
    }

    /**
     * 处理信息命令
     *
     * @param sender 命令发送者
     * @return 命令是否处理成功
     */
    private boolean handleInfoCommand(CommandSender sender) {
        sender.sendMessage("§a=== GivingItem 插件信息 ===");
        sender.sendMessage("§e版本: §f" + getDescription().getVersion());
        sender.sendMessage("§e作者: §f" + getDescription().getAuthors());
        sender.sendMessage("§e已配置物品数量: §f" + configManager.getItemDataList().size());
        sender.sendMessage("§e首次加入给予物品: §f" + (configManager.isGiveItemsOnFirstJoin() ? "§a启用" : "§c禁用"));
        sender.sendMessage("§e每次加入给予物品: §f" + (configManager.isGiveItemsOnEveryJoin() ? "§a启用" : "§c禁用"));
        sender.sendMessage("§e给予前清空背包: §f" + (configManager.isClearInventoryBeforeGiving() ? "§a启用" : "§c禁用"));
        sender.sendMessage("§e防止丢弃物品: §f" + (configManager.isPreventDrop() ? "§a启用" : "§c禁用"));
        sender.sendMessage("§e防止移动物品: §f" + (configManager.isPreventMove() ? "§a启用" : "§c禁用"));

        return true;
    }

    /**
     * 给玩家发放物品
     * 根据配置决定是否在给予物品前清空背包
     *
     * @param player 目标玩家
     */
    private void giveItemsToPlayer(Player player) {
        // 根据配置决定是否清空背包
        if (configManager.isClearInventoryBeforeGiving()) {
            clearPlayerMainInventory(player);

            if (configManager.isDebug()) {
                getLogger().info("已清空玩家 " + player.getName() + " 的主背包");
            }
        }

        for (GivingItemData itemData : configManager.getItemDataList()) {
            // 如果已清空背包，则跳过槽位检查；否则检查槽位是否已有插件物品
            if (!configManager.isClearInventoryBeforeGiving() &&
                ItemUtils.hasPluginItemInSlot(player, itemData.getSlot())) {
                continue;
            }

            // 创建物品
            org.bukkit.inventory.ItemStack item = ItemUtils.createItem(itemData);
            if (item != null) {
                // 设置到指定槽位
                ItemUtils.setItemToSlot(player, item, itemData.getSlot());
            }
        }
    }

    /**
     * 清空玩家的主背包（0-35槽位）
     * 不影响盔甲槽（36-39）和副手槽（40）
     *
     * @param player 目标玩家
     */
    private void clearPlayerMainInventory(Player player) {
        if (player == null) {
            return;
        }

        // 清空主背包的0-35槽位
        // 0-8: 快捷栏
        // 9-35: 主背包
        for (int slot = 0; slot <= 35; slot++) {
            player.getInventory().setItem(slot, null);
        }

        if (configManager.isDebug()) {
            getLogger().info("清空玩家 " + player.getName() + " 主背包（槽位0-35）");
        }
    }

    /**
     * 获取配置管理器
     *
     * @return 配置管理器实例
     */
    public ConfigManager getConfigManager() {
        return configManager;
    }
}
