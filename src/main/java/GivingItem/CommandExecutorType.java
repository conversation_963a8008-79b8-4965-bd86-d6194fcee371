package GivingItem;

/**
 * 命令执行器类型枚举
 * 定义了命令的执行方式
 *
 * <AUTHOR>
 * @version 1.0
 */
public enum CommandExecutorType {
    
    /**
     * 以玩家身份执行命令
     */
    PLAYER("player"),
    
    /**
     * 以控制台身份执行命令
     */
    CONSOLE("console"),
    
    /**
     * 以OP权限执行命令
     */
    OP("op");
    
    private final String configName;
    
    /**
     * 构造函数
     * 
     * @param configName 配置文件中的名称
     */
    CommandExecutorType(String configName) {
        this.configName = configName;
    }
    
    /**
     * 获取配置文件中的名称
     * 
     * @return 配置名称
     */
    public String getConfigName() {
        return configName;
    }
    
    /**
     * 根据配置名称获取枚举值
     * 
     * @param configName 配置名称
     * @return 对应的枚举值，如果未找到则返回PLAYER
     */
    public static CommandExecutorType fromConfigName(String configName) {
        if (configName == null) {
            return PLAYER;
        }
        
        for (CommandExecutorType type : values()) {
            if (type.configName.equalsIgnoreCase(configName)) {
                return type;
            }
        }
        
        return PLAYER; // 默认返回PLAYER
    }
}
