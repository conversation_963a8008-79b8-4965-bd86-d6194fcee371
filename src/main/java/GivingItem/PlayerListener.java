package GivingItem;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.inventory.ItemStack;

import java.util.List;

/**
 * 玩家事件监听器
 * 处理玩家加入、物品交互、物品丢弃等事件
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class PlayerListener implements Listener {
    
    private final GivingItem plugin;
    private final ConfigManager configManager;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     * @param configManager 配置管理器
     */
    public PlayerListener(GivingItem plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
    }
    
    /**
     * 处理玩家加入事件
     * 
     * @param event 玩家加入事件
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        
        // 检查是否需要给予物品
        if (!configManager.isGiveItemsOnFirstJoin()) {
            return;
        }
        
        // 检查是否是首次加入
        if (player.hasPlayedBefore()) {
            return;
        }
        
        // 延迟1秒给予物品，确保玩家完全加载
        Bukkit.getScheduler().runTaskLater(plugin, () -> giveItemsToPlayer(player), 20L);
        
        if (configManager.isDebug()) {
            plugin.getLogger().info("玩家 " + player.getName() + " 首次加入，准备给予物品");
        }
    }
    
    /**
     * 给玩家发放物品
     * 
     * @param player 目标玩家
     */
    private void giveItemsToPlayer(Player player) {
        List<GivingItemData> itemDataList = configManager.getItemDataList();
        
        for (GivingItemData itemData : itemDataList) {
            // 检查槽位是否已有插件物品
            if (ItemUtils.hasPluginItemInSlot(player, itemData.getSlot())) {
                continue;
            }
            
            // 创建物品
            ItemStack item = ItemUtils.createItem(itemData);
            if (item != null) {
                // 设置到指定槽位
                ItemUtils.setItemToSlot(player, item, itemData.getSlot());
                
                if (configManager.isDebug()) {
                    plugin.getLogger().info("给予玩家 " + player.getName() + " 物品: " + itemData.getName());
                }
            }
        }
    }
    
    /**
     * 处理玩家右键交互事件
     * 
     * @param event 玩家交互事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        // 检查是否是右键点击且手持物品
        if (!event.getAction().name().contains("RIGHT_CLICK") || item == null) {
            return;
        }
        
        // 检查是否是插件创建的物品
        if (!ItemUtils.isPluginItem(item)) {
            return;
        }
        
        // 查找对应的物品配置
        GivingItemData itemData = findItemDataByMaterial(item);
        if (itemData == null) {
            return;
        }
        
        // 取消事件，防止其他插件处理
        event.setCancelled(true);
        
        // 执行命令
        executeCommand(player, itemData);
        
        if (configManager.isDebug()) {
            plugin.getLogger().info("玩家 " + player.getName() + " 使用物品: " + itemData.getName());
        }
    }
    
    /**
     * 根据物品材质查找对应的配置数据
     * 
     * @param item 物品
     * @return 对应的配置数据，如果未找到返回null
     */
    private GivingItemData findItemDataByMaterial(ItemStack item) {
        List<GivingItemData> itemDataList = configManager.getItemDataList();
        
        for (GivingItemData itemData : itemDataList) {
            if (itemData.getMaterial() == item.getType()) {
                return itemData;
            }
        }
        
        return null;
    }
    
    /**
     * 执行命令
     * 
     * @param player 玩家
     * @param itemData 物品配置数据
     */
    private void executeCommand(Player player, GivingItemData itemData) {
        String command = ItemUtils.replacePlaceholders(itemData.getCommand(), player);
        
        switch (itemData.getExecutorType()) {
            case PLAYER:
                player.performCommand(command);
                break;
                
            case CONSOLE:
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
                break;
                
            case OP:
                boolean wasOp = player.isOp();
                try {
                    player.setOp(true);
                    player.performCommand(command);
                } finally {
                    player.setOp(wasOp);
                }
                break;
        }
    }
    
    /**
     * 处理玩家丢弃物品事件
     * 
     * @param event 丢弃物品事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerDropItem(PlayerDropItemEvent event) {
        if (!configManager.isPreventDrop()) {
            return;
        }
        
        ItemStack item = event.getItemDrop().getItemStack();
        
        // 检查是否是插件创建的物品
        if (ItemUtils.isPluginItem(item)) {
            event.setCancelled(true);
            
            if (configManager.isDebug()) {
                plugin.getLogger().info("阻止玩家 " + event.getPlayer().getName() + " 丢弃插件物品");
            }
        }
    }
    
    /**
     * 处理背包点击事件（防止移动物品）
     * 
     * @param event 背包点击事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!configManager.isPreventMove()) {
            return;
        }
        
        ItemStack currentItem = event.getCurrentItem();
        ItemStack cursorItem = event.getCursor();
        
        // 检查当前点击的物品或光标上的物品是否是插件物品
        boolean isCurrentPluginItem = ItemUtils.isPluginItem(currentItem);
        boolean isCursorPluginItem = ItemUtils.isPluginItem(cursorItem);
        
        if (isCurrentPluginItem || isCursorPluginItem) {
            event.setCancelled(true);
            
            if (configManager.isDebug() && event.getWhoClicked() instanceof Player) {
                Player player = (Player) event.getWhoClicked();
                plugin.getLogger().info("阻止玩家 " + player.getName() + " 移动插件物品");
            }
        }
    }
}
