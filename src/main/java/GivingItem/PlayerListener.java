package GivingItem;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.inventory.ItemStack;

import java.util.List;

/**
 * 玩家事件监听器
 * 处理玩家加入、物品交互、物品丢弃等事件
 *
 * <AUTHOR>
 * @version 1.0
 */
public class PlayerListener implements Listener {
    
    private final GivingItem plugin;
    private final ConfigManager configManager;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     * @param configManager 配置管理器
     */
    public PlayerListener(GivingItem plugin, ConfigManager configManager) {
        this.plugin = plugin;
        this.configManager = configManager;
    }
    
    /**
     * 处理玩家加入事件
     * 支持两种模式：首次加入给予物品 或 每次加入都给予物品
     *
     * @param event 玩家加入事件
     */
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        boolean shouldGiveItems = false;
        String reason = "";

        // 添加调试信息
        if (configManager.isDebug()) {
            plugin.getLogger().info("玩家 " + player.getName() + " 加入服务器");
            plugin.getLogger().info("首次加入模式: " + configManager.isGiveItemsOnFirstJoin());
            plugin.getLogger().info("每次加入模式: " + configManager.isGiveItemsOnEveryJoin());
            plugin.getLogger().info("玩家是否之前玩过: " + player.hasPlayedBefore());
            plugin.getLogger().info("配置的物品数量: " + configManager.getItemDataList().size());
        }

        // 检查是否每次加入都给予物品
        if (configManager.isGiveItemsOnEveryJoin()) {
            shouldGiveItems = true;
            reason = "每次加入模式";
        }
        // 检查是否首次加入给予物品
        else if (configManager.isGiveItemsOnFirstJoin() && !player.hasPlayedBefore()) {
            shouldGiveItems = true;
            reason = "首次加入";
        }

        // 添加调试信息
        if (configManager.isDebug()) {
            plugin.getLogger().info("是否应该给予物品: " + shouldGiveItems + " (原因: " + reason + ")");
        }

        // 如果需要给予物品
        if (shouldGiveItems) {
            // 延迟1秒给予物品，确保玩家完全加载
            Bukkit.getScheduler().runTaskLater(plugin, () -> giveItemsToPlayer(player), 20L);

            plugin.getLogger().info("玩家 " + player.getName() + " " + reason + "，准备给予物品");
        } else {
            // 添加未给予物品的原因日志
            if (configManager.isDebug()) {
                if (!configManager.isGiveItemsOnEveryJoin() && !configManager.isGiveItemsOnFirstJoin()) {
                    plugin.getLogger().info("未给予物品：两种模式都未启用");
                } else if (configManager.isGiveItemsOnFirstJoin() && player.hasPlayedBefore()) {
                    plugin.getLogger().info("未给予物品：玩家不是首次加入");
                }
            }
        }
    }
    
    /**
     * 给玩家发放物品
     * 根据配置决定是否在给予物品前清空背包
     *
     * @param player 目标玩家
     */
    private void giveItemsToPlayer(Player player) {
        plugin.getLogger().info("开始给玩家 " + player.getName() + " 发放物品");

        // 检查玩家是否在线
        if (player == null || !player.isOnline()) {
            plugin.getLogger().warning("玩家 " + (player != null ? player.getName() : "null") + " 不在线，无法给予物品");
            return;
        }

        // 根据配置决定是否清空背包
        if (configManager.isClearInventoryBeforeGiving()) {
            clearPlayerMainInventory(player);

            if (configManager.isDebug()) {
                plugin.getLogger().info("已清空玩家 " + player.getName() + " 的主背包");
            }
        }

        List<GivingItemData> itemDataList = configManager.getItemDataList();

        if (itemDataList == null || itemDataList.isEmpty()) {
            plugin.getLogger().warning("物品配置列表为空，无法给予物品");
            return;
        }

        plugin.getLogger().info("准备给予 " + itemDataList.size() + " 个物品");

        int successCount = 0;
        for (GivingItemData itemData : itemDataList) {
            if (configManager.isDebug()) {
                plugin.getLogger().info("处理物品: " + itemData.getName() + " 槽位: " + itemData.getSlot());
            }

            // 如果已清空背包，则跳过槽位检查；否则检查槽位是否已有插件物品
            if (!configManager.isClearInventoryBeforeGiving() &&
                ItemUtils.hasPluginItemInSlot(player, itemData.getSlot())) {
                if (configManager.isDebug()) {
                    plugin.getLogger().info("槽位 " + itemData.getSlot() + " 已有插件物品，跳过");
                }
                continue;
            }

            // 创建物品
            ItemStack item = ItemUtils.createItem(itemData);
            if (item != null) {
                // 设置到指定槽位
                ItemUtils.setItemToSlot(player, item, itemData.getSlot());
                successCount++;

                plugin.getLogger().info("成功给予玩家 " + player.getName() + " 物品: " + itemData.getName() + " 到槽位 " + itemData.getSlot());
            } else {
                plugin.getLogger().warning("创建物品失败: " + itemData.getName());
            }
        }

        plugin.getLogger().info("完成给予物品，成功给予 " + successCount + " 个物品");
    }

    /**
     * 清空玩家的主背包（0-35槽位）
     * 不影响盔甲槽（36-39）和副手槽（40）
     *
     * @param player 目标玩家
     */
    private void clearPlayerMainInventory(Player player) {
        if (player == null) {
            return;
        }

        // 清空主背包的0-35槽位
        // 0-8: 快捷栏
        // 9-35: 主背包
        for (int slot = 0; slot <= 35; slot++) {
            player.getInventory().setItem(slot, null);
        }

        if (configManager.isDebug()) {
            plugin.getLogger().info("清空玩家 " + player.getName() + " 主背包（槽位0-35）");
        }
    }
    
    /**
     * 处理玩家右键交互事件
     * 
     * @param event 玩家交互事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();
        
        // 检查是否是右键点击且手持物品
        if (!event.getAction().name().contains("RIGHT_CLICK") || item == null) {
            return;
        }
        
        // 检查是否是插件创建的物品
        if (!ItemUtils.isPluginItem(item)) {
            return;
        }
        
        // 查找对应的物品配置
        GivingItemData itemData = findItemDataByMaterial(item);
        if (itemData == null) {
            return;
        }
        
        // 取消事件，防止其他插件处理
        event.setCancelled(true);
        
        // 执行命令
        executeCommand(player, itemData);
        
        if (configManager.isDebug()) {
            plugin.getLogger().info("玩家 " + player.getName() + " 使用物品: " + itemData.getName());
        }
    }
    
    /**
     * 根据物品材质查找对应的配置数据
     * 
     * @param item 物品
     * @return 对应的配置数据，如果未找到返回null
     */
    private GivingItemData findItemDataByMaterial(ItemStack item) {
        List<GivingItemData> itemDataList = configManager.getItemDataList();
        
        for (GivingItemData itemData : itemDataList) {
            if (itemData.getMaterial() == item.getType()) {
                return itemData;
            }
        }
        
        return null;
    }
    
    /**
     * 执行命令
     * 
     * @param player 玩家
     * @param itemData 物品配置数据
     */
    private void executeCommand(Player player, GivingItemData itemData) {
        String command = ItemUtils.replacePlaceholders(itemData.getCommand(), player);
        
        switch (itemData.getExecutorType()) {
            case PLAYER:
                player.performCommand(command);
                break;
                
            case CONSOLE:
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
                break;
                
            case OP:
                boolean wasOp = player.isOp();
                try {
                    player.setOp(true);
                    player.performCommand(command);
                } finally {
                    player.setOp(wasOp);
                }
                break;
        }
    }
    
    /**
     * 处理玩家丢弃物品事件
     * 
     * @param event 丢弃物品事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerDropItem(PlayerDropItemEvent event) {
        if (!configManager.isPreventDrop()) {
            return;
        }
        
        ItemStack item = event.getItemDrop().getItemStack();
        
        // 检查是否是插件创建的物品
        if (ItemUtils.isPluginItem(item)) {
            event.setCancelled(true);
            
            if (configManager.isDebug()) {
                plugin.getLogger().info("阻止玩家 " + event.getPlayer().getName() + " 丢弃插件物品");
            }
        }
    }
    
    /**
     * 处理背包点击事件（防止移动物品）
     * 
     * @param event 背包点击事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onInventoryClick(InventoryClickEvent event) {
        if (!configManager.isPreventMove()) {
            return;
        }
        
        ItemStack currentItem = event.getCurrentItem();
        ItemStack cursorItem = event.getCursor();
        
        // 检查当前点击的物品或光标上的物品是否是插件物品
        boolean isCurrentPluginItem = ItemUtils.isPluginItem(currentItem);
        boolean isCursorPluginItem = ItemUtils.isPluginItem(cursorItem);
        
        if (isCurrentPluginItem || isCursorPluginItem) {
            event.setCancelled(true);
            
            if (configManager.isDebug() && event.getWhoClicked() instanceof Player) {
                Player player = (Player) event.getWhoClicked();
                plugin.getLogger().info("阻止玩家 " + player.getName() + " 移动插件物品");
            }
        }
    }
}
