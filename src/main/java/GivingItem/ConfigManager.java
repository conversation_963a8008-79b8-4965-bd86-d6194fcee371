package GivingItem;

import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;

/**
 * 配置管理类
 * 负责读取和验证配置文件
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ConfigManager {
    
    private final JavaPlugin plugin;
    private FileConfiguration config;
    private List<GivingItemData> itemDataList;
    
    // 配置选项
    private boolean giveItemsOnFirstJoin;
    private boolean preventDrop;
    private boolean preventMove;
    private boolean debug;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     */
    public ConfigManager(JavaPlugin plugin) {
        this.plugin = plugin;
        this.itemDataList = new ArrayList<>();
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    public void loadConfig() {
        // 保存默认配置文件
        plugin.saveDefaultConfig();
        
        // 重新加载配置
        plugin.reloadConfig();
        config = plugin.getConfig();
        
        // 加载设置
        loadSettings();
        
        // 加载物品配置
        loadItems();
        
        if (debug) {
            plugin.getLogger().info("配置文件加载完成，共加载 " + itemDataList.size() + " 个物品配置");
        }
    }
    
    /**
     * 加载插件设置
     */
    private void loadSettings() {
        ConfigurationSection settingsSection = config.getConfigurationSection("settings");
        if (settingsSection != null) {
            giveItemsOnFirstJoin = settingsSection.getBoolean("give-items-on-first-join", true);
            preventDrop = settingsSection.getBoolean("prevent-drop", true);
            preventMove = settingsSection.getBoolean("prevent-move", true);
            debug = settingsSection.getBoolean("debug", false);
        } else {
            // 使用默认值
            giveItemsOnFirstJoin = true;
            preventDrop = true;
            preventMove = true;
            debug = false;
        }
    }
    
    /**
     * 加载物品配置
     */
    private void loadItems() {
        itemDataList.clear();

        List<?> itemsList = config.getList("items");
        if (itemsList == null || itemsList.isEmpty()) {
            plugin.getLogger().warning("配置文件中未找到物品配置！");
            return;
        }

        for (int i = 0; i < itemsList.size(); i++) {
            Object itemObj = itemsList.get(i);

            // 处理Map类型的配置项
            if (itemObj instanceof java.util.Map) {
                @SuppressWarnings("unchecked")
                java.util.Map<String, Object> itemMap = (java.util.Map<String, Object>) itemObj;
                GivingItemData itemData = parseItemDataFromMap(itemMap);

                if (itemData != null && itemData.isValid()) {
                    itemDataList.add(itemData);
                    if (debug) {
                        plugin.getLogger().info("加载物品配置: " + itemData.toString());
                    }
                } else {
                    plugin.getLogger().warning("无效的物品配置，索引: " + i);
                }
            }
        }
    }
    
    /**
     * 从Map解析物品配置
     *
     * @param itemMap 配置Map
     * @return 物品数据对象，如果解析失败返回null
     */
    private GivingItemData parseItemDataFromMap(java.util.Map<String, Object> itemMap) {
        try {
            // 解析材质
            String materialName = (String) itemMap.get("material");
            if (materialName == null) {
                plugin.getLogger().warning("物品配置缺少material字段");
                return null;
            }

            Material material = Material.getMaterial(materialName.toUpperCase());
            if (material == null) {
                plugin.getLogger().warning("无效的材质: " + materialName);
                return null;
            }

            // 解析名称
            String name = (String) itemMap.getOrDefault("name", "未命名物品");

            // 解析槽位
            Object slotObj = itemMap.get("slot");
            int slot = 0;
            if (slotObj instanceof Number) {
                slot = ((Number) slotObj).intValue();
            }

            if (slot < 0 || slot > 35) {
                plugin.getLogger().warning("无效的槽位: " + slot + "，必须在0-35之间");
                return null;
            }

            // 解析命令
            String command = (String) itemMap.get("command");
            if (command == null || command.trim().isEmpty()) {
                plugin.getLogger().warning("物品配置缺少command字段");
                return null;
            }

            // 解析执行器类型
            String executorName = (String) itemMap.getOrDefault("executor", "PLAYER");
            CommandExecutorType executorType = CommandExecutorType.fromConfigName(executorName);

            return new GivingItemData(material, name, slot, command, executorType);

        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "解析物品配置时发生错误", e);
            return null;
        }
    }

    /**
     * 解析单个物品配置
     *
     * @param section 配置节
     * @return 物品数据对象，如果解析失败返回null
     */
    private GivingItemData parseItemData(ConfigurationSection section) {
        try {
            // 解析材质
            String materialName = section.getString("material");
            if (materialName == null) {
                plugin.getLogger().warning("物品配置缺少material字段");
                return null;
            }
            
            Material material = Material.getMaterial(materialName.toUpperCase());
            if (material == null) {
                plugin.getLogger().warning("无效的材质: " + materialName);
                return null;
            }
            
            // 解析名称
            String name = section.getString("name", "未命名物品");
            
            // 解析槽位
            int slot = section.getInt("slot", 0);
            if (slot < 0 || slot > 35) {
                plugin.getLogger().warning("无效的槽位: " + slot + "，必须在0-35之间");
                return null;
            }
            
            // 解析命令
            String command = section.getString("command");
            if (command == null || command.trim().isEmpty()) {
                plugin.getLogger().warning("物品配置缺少command字段");
                return null;
            }
            
            // 解析执行器类型
            String executorName = section.getString("executor", "PLAYER");
            CommandExecutorType executorType = CommandExecutorType.fromConfigName(executorName);
            
            return new GivingItemData(material, name, slot, command, executorType);
            
        } catch (Exception e) {
            plugin.getLogger().log(Level.WARNING, "解析物品配置时发生错误", e);
            return null;
        }
    }
    
    /**
     * 获取物品配置列表
     * 
     * @return 物品配置列表
     */
    public List<GivingItemData> getItemDataList() {
        return new ArrayList<>(itemDataList);
    }
    
    /**
     * 是否在首次加入时给予物品
     * 
     * @return 配置值
     */
    public boolean isGiveItemsOnFirstJoin() {
        return giveItemsOnFirstJoin;
    }
    
    /**
     * 是否阻止丢弃物品
     * 
     * @return 配置值
     */
    public boolean isPreventDrop() {
        return preventDrop;
    }
    
    /**
     * 是否阻止移动物品
     * 
     * @return 配置值
     */
    public boolean isPreventMove() {
        return preventMove;
    }
    
    /**
     * 是否开启调试模式
     * 
     * @return 配置值
     */
    public boolean isDebug() {
        return debug;
    }
}
