package GivingItem;

import org.bukkit.ChatColor;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.List;

/**
 * 物品工具类
 * 提供物品创建、检查和管理的工具方法
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ItemUtils {
    
    /** 物品标识符，用于识别插件创建的物品 */
    private static final String ITEM_IDENTIFIER = "§r§f§r§f§r§f";
    
    /**
     * 根据配置数据创建物品
     * 
     * @param itemData 物品配置数据
     * @return 创建的物品
     */
    public static ItemStack createItem(GivingItemData itemData) {
        if (itemData == null || !itemData.isValid()) {
            return null;
        }
        
        ItemStack item = new ItemStack(itemData.getMaterial(), 1);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            // 设置显示名称，支持颜色代码
            String displayName = ChatColor.translateAlternateColorCodes('&', itemData.getName());
            meta.setDisplayName(displayName);
            
            // 添加隐藏的标识符到Lore中，用于识别这是插件创建的物品
            List<String> lore = Arrays.asList(ITEM_IDENTIFIER);
            meta.setLore(lore);
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * 检查物品是否是插件创建的物品
     * 
     * @param item 要检查的物品
     * @return 如果是插件创建的物品返回true，否则返回false
     */
    public static boolean isPluginItem(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasLore()) {
            return false;
        }
        
        List<String> lore = meta.getLore();
        return lore != null && lore.contains(ITEM_IDENTIFIER);
    }
    
    /**
     * 给玩家指定槽位设置物品
     * 
     * @param player 目标玩家
     * @param item 要设置的物品
     * @param slot 槽位位置
     */
    public static void setItemToSlot(Player player, ItemStack item, int slot) {
        if (player == null || item == null || slot < 0 || slot > 35) {
            return;
        }
        
        player.getInventory().setItem(slot, item);
    }
    
    /**
     * 检查玩家指定槽位是否已有插件物品
     * 
     * @param player 目标玩家
     * @param slot 槽位位置
     * @return 如果槽位已有插件物品返回true，否则返回false
     */
    public static boolean hasPluginItemInSlot(Player player, int slot) {
        if (player == null || slot < 0 || slot > 35) {
            return false;
        }
        
        ItemStack item = player.getInventory().getItem(slot);
        return isPluginItem(item);
    }
    
    /**
     * 替换命令中的占位符
     * 
     * @param command 原始命令
     * @param player 玩家对象
     * @return 替换占位符后的命令
     */
    public static String replacePlaceholders(String command, Player player) {
        if (command == null || player == null) {
            return command;
        }
        
        return command.replace("%player%", player.getName())
                     .replace("%uuid%", player.getUniqueId().toString())
                     .replace("%world%", player.getWorld().getName());
    }
}
