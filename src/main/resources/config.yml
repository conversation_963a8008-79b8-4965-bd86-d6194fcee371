# 物品配置列表
items:
  # 传送菜单指南针
  - material: COMPASS
    name: "&a传送菜单"
    slot: 4
    command: "warp gui"
    executor: PLAYER

  # 帮助手册
  - material: BOOK
    name: "&b帮助手册"
    slot: 8
    command: "help"
    executor: PLAYER

# 插件设置
settings:
  # 是否在玩家首次加入时给予物品
  # 注意：此选项与 give-items-on-every-join 可以同时启用
  give-items-on-first-join: true

  # 是否在玩家每次加入服务器时都给予物品
  # true: 每次进服都给予物品（包括首次加入）
  # false: 只在首次加入时给予物品（需要 give-items-on-first-join 为 true）
  # 适用场景：临时活动、测试服务器、或需要每次重新分发物品的场合
  give-items-on-every-join: false

  # 是否在给予物品前清空玩家背包
  # 注意：这只会清空主背包（0-35槽位），不会影响盔甲槽和副手槽
  # 在两种给予模式下都会生效（首次加入或每次加入）
  # 建议在新服务器或重置服务器时启用此功能
  clear-inventory-before-giving: false

  # 是否阻止玩家丢弃这些物品
  prevent-drop: true

  # 是否阻止玩家移动这些物品
  prevent-move: true

  # 调试模式
  debug: true