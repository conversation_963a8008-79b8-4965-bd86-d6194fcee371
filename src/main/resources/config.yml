# GivingItem 插件配置文件
# 配置玩家首次加入时获得的物品

# 物品配置列表
items:
  # 传送菜单指南针
  - material: COMPASS
    name: "&a传送菜单"
    slot: 4
    command: "warp gui"
    executor: PLAYER

  # 帮助手册
  - material: BOOK
    name: "&b帮助手册"
    slot: 8
    command: "help"
    executor: PLAYER

# 插件设置
settings:
  # 是否在玩家首次加入时给予物品
  give-items-on-first-join: true

  # 是否阻止玩家丢弃这些物品
  prevent-drop: true

  # 是否阻止玩家移动这些物品
  prevent-move: true

  # 调试模式
  debug: false