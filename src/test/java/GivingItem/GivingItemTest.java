package GivingItem;

import org.bukkit.Material;
import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

/**
 * GivingItem 插件测试类
 * 测试核心功能的正确性
 *
 * <AUTHOR>
 * @version 1.0
 */
public class GivingItemTest {
    
    private GivingItemData testItemData;
    
    @Before
    public void setUp() {
        // 创建测试用的物品数据
        testItemData = new GivingItemData(
            Material.COMPASS,
            "&a传送菜单",
            4,
            "warp gui",
            CommandExecutorType.PLAYER
        );
    }
    
    /**
     * 测试物品数据的创建和验证
     */
    @Test
    public void testGivingItemDataCreation() {
        assertNotNull("物品数据不应为null", testItemData);
        assertEquals("材质应为COMPASS", Material.COMPASS, testItemData.getMaterial());
        assertEquals("名称应正确", "&a传送菜单", testItemData.getName());
        assertEquals("槽位应为4", 4, testItemData.getSlot());
        assertEquals("命令应正确", "warp gui", testItemData.getCommand());
        assertEquals("执行器类型应为PLAYER", CommandExecutorType.PLAYER, testItemData.getExecutorType());
    }
    
    /**
     * 测试物品数据验证功能
     */
    @Test
    public void testGivingItemDataValidation() {
        assertTrue("有效的物品数据应通过验证", testItemData.isValid());
        
        // 测试无效的物品数据
        GivingItemData invalidData1 = new GivingItemData(null, "测试", 0, "test", CommandExecutorType.PLAYER);
        assertFalse("材质为null的数据应无效", invalidData1.isValid());
        
        GivingItemData invalidData2 = new GivingItemData(Material.STONE, "", 0, "test", CommandExecutorType.PLAYER);
        assertFalse("名称为空的数据应无效", invalidData2.isValid());
        
        GivingItemData invalidData3 = new GivingItemData(Material.STONE, "测试", -1, "test", CommandExecutorType.PLAYER);
        assertFalse("槽位为负数的数据应无效", invalidData3.isValid());
        
        GivingItemData invalidData4 = new GivingItemData(Material.STONE, "测试", 0, "", CommandExecutorType.PLAYER);
        assertFalse("命令为空的数据应无效", invalidData4.isValid());
    }
    
    /**
     * 测试命令执行器类型枚举
     */
    @Test
    public void testCommandExecutorType() {
        assertEquals("PLAYER类型配置名应正确", "player", CommandExecutorType.PLAYER.getConfigName());
        assertEquals("CONSOLE类型配置名应正确", "console", CommandExecutorType.CONSOLE.getConfigName());
        assertEquals("OP类型配置名应正确", "op", CommandExecutorType.OP.getConfigName());
        
        // 测试从配置名获取枚举值
        assertEquals("应正确解析player", CommandExecutorType.PLAYER, CommandExecutorType.fromConfigName("player"));
        assertEquals("应正确解析PLAYER", CommandExecutorType.PLAYER, CommandExecutorType.fromConfigName("PLAYER"));
        assertEquals("应正确解析console", CommandExecutorType.CONSOLE, CommandExecutorType.fromConfigName("console"));
        assertEquals("应正确解析op", CommandExecutorType.OP, CommandExecutorType.fromConfigName("op"));
        
        // 测试无效配置名
        assertEquals("无效配置名应返回PLAYER", CommandExecutorType.PLAYER, CommandExecutorType.fromConfigName("invalid"));
        assertEquals("null配置名应返回PLAYER", CommandExecutorType.PLAYER, CommandExecutorType.fromConfigName(null));
    }
    
    /**
     * 测试占位符替换功能
     */
    @Test
    public void testPlaceholderReplacement() {
        // 由于需要Player对象，这里只能测试基本的字符串替换逻辑
        String command = "tp %player% spawn";
        String expected = "tp TestPlayer spawn";
        
        // 模拟替换（实际实现中需要Player对象）
        String result = command.replace("%player%", "TestPlayer");
        assertEquals("占位符应正确替换", expected, result);
    }
    
    /**
     * 测试物品数据的toString方法
     */
    @Test
    public void testToString() {
        String result = testItemData.toString();
        assertNotNull("toString结果不应为null", result);
        assertTrue("toString应包含材质信息", result.contains("COMPASS"));
        assertTrue("toString应包含名称信息", result.contains("传送菜单"));
        assertTrue("toString应包含槽位信息", result.contains("4"));
        assertTrue("toString应包含命令信息", result.contains("warp gui"));
        assertTrue("toString应包含执行器类型信息", result.contains("PLAYER"));
    }

    /**
     * 测试背包槽位范围验证
     */
    @Test
    public void testInventorySlotValidation() {
        // 测试有效槽位范围
        assertTrue("槽位0应该有效", isValidMainInventorySlot(0));
        assertTrue("槽位8应该有效", isValidMainInventorySlot(8));
        assertTrue("槽位9应该有效", isValidMainInventorySlot(9));
        assertTrue("槽位35应该有效", isValidMainInventorySlot(35));

        // 测试无效槽位范围
        assertFalse("槽位-1应该无效", isValidMainInventorySlot(-1));
        assertFalse("槽位36应该无效", isValidMainInventorySlot(36));
        assertFalse("槽位40应该无效", isValidMainInventorySlot(40));
    }

    /**
     * 测试给予物品的逻辑判断
     */
    @Test
    public void testGiveItemsLogic() {
        // 测试首次加入模式
        assertTrue("首次加入模式应该给予物品", shouldGiveItemsOnFirstJoin(true, false, false));
        assertFalse("首次加入模式，老玩家不应该给予物品", shouldGiveItemsOnFirstJoin(true, false, true));

        // 测试每次加入模式
        assertTrue("每次加入模式，新玩家应该给予物品", shouldGiveItemsOnEveryJoin(false, true, false));
        assertTrue("每次加入模式，老玩家也应该给予物品", shouldGiveItemsOnEveryJoin(false, true, true));

        // 测试两种模式都启用
        assertTrue("两种模式都启用，新玩家应该给予物品", shouldGiveItemsOnEveryJoin(true, true, false));
        assertTrue("两种模式都启用，老玩家应该给予物品", shouldGiveItemsOnEveryJoin(true, true, true));

        // 测试两种模式都禁用
        assertFalse("两种模式都禁用，不应该给予物品", shouldGiveItemsOnFirstJoin(false, false, false));
        assertFalse("两种模式都禁用，不应该给予物品", shouldGiveItemsOnFirstJoin(false, false, true));
    }

    /**
     * 辅助方法：验证槽位是否为主背包槽位
     *
     * @param slot 槽位编号
     * @return 是否为主背包槽位（0-35）
     */
    private boolean isValidMainInventorySlot(int slot) {
        return slot >= 0 && slot <= 35;
    }

    /**
     * 辅助方法：模拟首次加入模式的逻辑判断
     *
     * @param giveOnFirstJoin 是否启用首次加入给予
     * @param giveOnEveryJoin 是否启用每次加入给予
     * @param hasPlayedBefore 玩家是否之前玩过
     * @return 是否应该给予物品
     */
    private boolean shouldGiveItemsOnFirstJoin(boolean giveOnFirstJoin, boolean giveOnEveryJoin, boolean hasPlayedBefore) {
        if (giveOnEveryJoin) {
            return true;
        } else if (giveOnFirstJoin && !hasPlayedBefore) {
            return true;
        }
        return false;
    }

    /**
     * 辅助方法：模拟每次加入模式的逻辑判断
     *
     * @param giveOnFirstJoin 是否启用首次加入给予
     * @param giveOnEveryJoin 是否启用每次加入给予
     * @param hasPlayedBefore 玩家是否之前玩过
     * @return 是否应该给予物品
     */
    private boolean shouldGiveItemsOnEveryJoin(boolean giveOnFirstJoin, boolean giveOnEveryJoin, boolean hasPlayedBefore) {
        return shouldGiveItemsOnFirstJoin(giveOnFirstJoin, giveOnEveryJoin, hasPlayedBefore);
    }
}
