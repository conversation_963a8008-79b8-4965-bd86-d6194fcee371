@echo off
echo 正在编译 GivingItem 插件...

:: 创建输出目录
if not exist "build" mkdir build
if not exist "build\classes" mkdir build\classes

:: 编译Java文件
javac -cp "lib\spigot-1.8.8-R0.1-SNAPSHOT-latest.jar" -d build\classes src\main\java\GivingItem\*.java

if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

:: 复制资源文件
xcopy "src\main\resources\*" "build\classes\" /Y /Q

:: 创建JAR文件
cd build\classes
jar cf ..\GivingItem-1.0.jar *
cd ..\..

echo 编译完成！插件文件: build\GivingItem-1.0.jar
pause
