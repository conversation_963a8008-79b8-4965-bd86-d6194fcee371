# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# IDE
.idea/
*.iml
*.ipr
*.iws
.vscode/
.settings/
.project
.classpath

# OS
.DS_Store
Thumbs.db

# Java
*.class
*.jar
!lib/*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar

# Logs
*.log

# Temporary files
*.tmp
*.temp
*~

# Build tools
build/
dist/
out/
